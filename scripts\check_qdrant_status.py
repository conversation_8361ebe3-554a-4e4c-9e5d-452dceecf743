#!/usr/bin/env python3
"""
Qdrant状态检查脚本
验证Qdrant服务器连接和集合状态

使用方法:
    python scripts/check_qdrant_status.py
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from backend.config.settings import Settings as AppSettings
    settings = AppSettings()
except ImportError:
    print("警告: 无法导入项目配置，使用默认配置")
    class DefaultSettings:
        qdrant_host = "localhost"
        qdrant_port = 6333
        qdrant_api_key = ""
        collection_name = "documents"
    settings = DefaultSettings()


class QdrantStatusChecker:
    """Qdrant状态检查器"""
    
    def __init__(self):
        self.client = None
        
    def check_all(self):
        """检查所有状态"""
        print("🔍 Qdrant状态检查")
        print("=" * 50)
        
        # 显示配置
        self._show_config()
        
        # 检查连接
        if not self._check_connection():
            return False
        
        # 检查服务器信息
        self._check_server_info()
        
        # 检查集合
        self._check_collections()
        
        # 检查特定集合
        self._check_target_collection()
        
        print("✅ Qdrant状态检查完成")
        return True
    
    def _show_config(self):
        """显示配置信息"""
        print(f"📋 配置信息:")
        print(f"  - Qdrant主机: {settings.qdrant_host}")
        print(f"  - Qdrant端口: {settings.qdrant_port}")
        print(f"  - API密钥: {'已设置' if settings.qdrant_api_key else '未设置'}")
        print(f"  - 目标集合: {settings.collection_name}")
        print()
    
    def _check_connection(self):
        """检查Qdrant连接"""
        print("🔗 检查Qdrant连接...")
        
        try:
            # 尝试导入qdrant_client
            import qdrant_client
            
            # 创建客户端
            self.client = qdrant_client.QdrantClient(
                host=settings.qdrant_host,
                port=settings.qdrant_port,
                api_key=settings.qdrant_api_key if settings.qdrant_api_key else None,
                timeout=10
            )

            # 测试连接 - 使用简单的集合列表获取来测试连接
            collections = self.client.get_collections()
            print(f"✅ Qdrant连接成功")
            print(f"   - 地址: {settings.qdrant_host}:{settings.qdrant_port}")
            return True
            
        except ImportError:
            print("❌ qdrant_client包未安装")
            print("   请运行: pip install qdrant-client")
            return False
        except Exception as e:
            print(f"❌ Qdrant连接失败: {e}")
            print("   请检查:")
            print("   1. Qdrant服务器是否运行")
            print("   2. 主机和端口配置是否正确")
            print("   3. 网络连接是否正常")
            return False
    
    def _check_server_info(self):
        """检查服务器信息"""
        print("ℹ️  检查服务器信息...")
        
        try:
            # 获取版本信息
            try:
                import requests
                response = requests.get(f"http://{settings.qdrant_host}:{settings.qdrant_port}/")
                if response.status_code == 200:
                    info = response.json()
                    print(f"   - 版本: {info.get('version', '未知')}")
                    print(f"   - 标题: {info.get('title', '未知')}")
                else:
                    print(f"   - HTTP状态: {response.status_code}")
            except Exception as e:
                print(f"   - 无法获取版本信息: {e}")

        except Exception as e:
            print(f"⚠️  获取服务器信息失败: {e}")
    
    def _check_collections(self):
        """检查所有集合"""
        print("📚 检查集合列表...")
        
        try:
            collections = self.client.get_collections()
            
            if not collections.collections:
                print("   - 无集合")
                return
            
            print(f"   - 集合数量: {len(collections.collections)}")
            for collection in collections.collections:
                print(f"     * {collection.name}")
                
        except Exception as e:
            print(f"⚠️  获取集合列表失败: {e}")
    
    def _check_target_collection(self):
        """检查目标集合"""
        print(f"🎯 检查目标集合: {settings.collection_name}")
        
        try:
            # 检查集合是否存在
            collections = self.client.get_collections()
            collection_names = [c.name for c in collections.collections]
            
            if settings.collection_name not in collection_names:
                print(f"   - 集合不存在: {settings.collection_name}")
                print("   - 这是正常的，应用启动时会自动创建")
                return
            
            # 获取集合详细信息
            collection_info = self.client.get_collection(settings.collection_name)
            
            print(f"   - 集合存在: {settings.collection_name}")
            print(f"   - 向量维度: {collection_info.config.params.vectors.size}")
            print(f"   - 距离度量: {collection_info.config.params.vectors.distance}")
            print(f"   - 点数量: {collection_info.points_count}")
            print(f"   - 索引状态: {collection_info.status}")
            
            # 如果有数据，显示一些样本
            if collection_info.points_count > 0:
                self._show_sample_points()
                
        except Exception as e:
            print(f"⚠️  检查目标集合失败: {e}")
    
    def _show_sample_points(self):
        """显示样本点"""
        print("   - 样本数据:")
        
        try:
            # 获取前3个点
            result = self.client.scroll(
                collection_name=settings.collection_name,
                limit=3,
                with_payload=True,
                with_vectors=False
            )
            
            points = result[0]
            for i, point in enumerate(points, 1):
                print(f"     {i}. ID: {point.id}")
                if point.payload:
                    # 显示关键元数据
                    filename = point.payload.get('filename', '未知')
                    print(f"        文件名: {filename}")
                    
        except Exception as e:
            print(f"        获取样本数据失败: {e}")


def main():
    """主函数"""
    checker = QdrantStatusChecker()
    success = checker.check_all()
    
    if not success:
        print()
        print("💡 故障排除建议:")
        print("   1. 启动Qdrant服务器:")
        print("      docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest")
        print("   2. 检查端口是否被占用:")
        print("      netstat -tulpn | grep 6333")
        print("   3. 安装依赖包:")
        print("      pip install qdrant-client")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
