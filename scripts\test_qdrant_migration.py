#!/usr/bin/env python3
"""
Qdrant迁移功能测试脚本
验证迁移后的RAG系统功能是否正常

使用方法:
    python scripts/test_qdrant_migration.py
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

BASE_URL = "http://localhost:8000/api/v1"


class QdrantMigrationTester:
    """Qdrant迁移功能测试器"""
    
    def __init__(self):
        self.base_url = BASE_URL
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 Qdrant迁移功能测试")
        print("=" * 50)
        
        tests = [
            ("系统状态检查", self.test_system_status),
            ("文档加载测试", self.test_document_loading),
            ("文档查询测试", self.test_document_query),
            ("文档管理测试", self.test_document_management),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}...")
            try:
                if test_func():
                    print(f"✅ {test_name} - 通过")
                    passed += 1
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 错误: {e}")
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！Qdrant迁移成功！")
            return True
        else:
            print("⚠️  部分测试失败，请检查问题")
            return False
    
    def test_system_status(self):
        """测试系统状态"""
        try:
            response = requests.get(f"{self.base_url}/status", timeout=10)
            if response.status_code != 200:
                print(f"   状态码错误: {response.status_code}")
                return False
            
            data = response.json()
            print(f"   - 状态: {data.get('status')}")
            print(f"   - 文档数量: {data.get('documents_count')}")
            print(f"   - 存储大小: {data.get('storage_size')}")
            print(f"   - 集合名称: {data.get('collection_name')}")
            
            # 检查是否包含Qdrant相关信息
            if 'vector_store' in data:
                print(f"   - 向量存储: {data.get('vector_store')}")
            if 'qdrant_host' in data:
                print(f"   - Qdrant主机: {data.get('qdrant_host')}")
            
            return data.get('status') == 'ok'
            
        except Exception as e:
            print(f"   请求失败: {e}")
            return False
    
    def test_document_loading(self):
        """测试文档加载"""
        try:
            response = requests.post(f"{self.base_url}/documents/load", timeout=60)
            if response.status_code != 200:
                print(f"   状态码错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                return False
            
            data = response.json()
            print(f"   - 成功: {data.get('success')}")
            print(f"   - 处理文件数: {data.get('documents_processed')}")
            print(f"   - 总块数: {data.get('total_chunks')}")
            
            if data.get('replaced_files'):
                print(f"   - 替换文件数: {len(data.get('replaced_files'))}")
            if data.get('new_files'):
                print(f"   - 新文件数: {len(data.get('new_files'))}")
            
            return data.get('success') and data.get('documents_processed', 0) > 0
            
        except Exception as e:
            print(f"   请求失败: {e}")
            return False
    
    def test_document_query(self):
        """测试文档查询"""
        try:
            # 先检查是否有文档
            status_response = requests.get(f"{self.base_url}/status", timeout=10)
            if status_response.status_code == 200:
                status_data = status_response.json()
                if status_data.get('documents_count', 0) == 0:
                    print("   - 跳过查询测试：无文档数据")
                    return True
            
            # 执行查询测试
            query_data = {
                "query": "什么是人工智能？",
                "max_results": 3
            }
            
            response = requests.post(
                f"{self.base_url}/query", 
                json=query_data,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"   状态码错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                return False
            
            data = response.json()
            print(f"   - 成功: {data.get('success')}")
            print(f"   - 答案长度: {len(data.get('answer', ''))}")
            print(f"   - 源文档数: {len(data.get('sources', []))}")
            
            return len(data.get('answer', '')) > 0
            
        except Exception as e:
            print(f"   请求失败: {e}")
            return False
    

    
    def test_document_management(self):
        """测试文档管理功能"""
        try:
            # 测试文档列表
            response = requests.get(f"{self.base_url}/documents/list", timeout=10)
            if response.status_code != 200:
                print(f"   文档列表状态码错误: {response.status_code}")
                return False
            
            data = response.json()
            print(f"   - 文档列表成功: {data.get('success')}")
            print(f"   - 文档数量: {len(data.get('documents', []))}")
            
            # 如果有文档，测试文档详情
            documents = data.get('documents', [])
            if documents:
                first_doc = documents[0]
                filename = first_doc.get('filename')
                
                detail_response = requests.get(
                    f"{self.base_url}/documents/{filename}", 
                    timeout=10
                )
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"   - 文档详情成功: {detail_data.get('success')}")
                else:
                    print(f"   - 文档详情失败: {detail_response.status_code}")
            
            return data.get('success')
            
        except Exception as e:
            print(f"   请求失败: {e}")
            return False


def main():
    """主函数"""
    print("等待服务器启动...")
    time.sleep(2)
    
    # 检查服务器是否可访问
    try:
        response = requests.get(f"{BASE_URL}/status", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器不可访问，状态码: {response.status_code}")
            print("请确保应用程序正在运行在 http://localhost:8000")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保应用程序正在运行在 http://localhost:8000")
        sys.exit(1)
    
    tester = QdrantMigrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 迁移验证完成！")
        print("📝 建议:")
        print("   1. 监控系统运行一段时间")
        print("   2. 测试更多复杂查询")
        print("   3. 验证性能是否有改善")
    else:
        print("\n⚠️  发现问题，请检查:")
        print("   1. Qdrant服务器是否正常运行")
        print("   2. 应用程序日志是否有错误")
        print("   3. 网络连接是否正常")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
